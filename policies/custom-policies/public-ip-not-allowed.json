{"properties": {"displayName": "Deny Public IP assignment to Virtual Machines", "policyType": "Custom", "mode": "Indexed", "description": "Prevent Public IP assignment to Virtual Machine Network Interfaces, with exceptions for Azure services like Application Gateway, Firewall, VPN Gateway, Bastion, Load Balancer, and NAT Gateway", "metadata": {"version": "1.0.0", "category": "Network"}, "parameters": {"effect": {"type": "String", "metadata": {"displayName": "Effect", "description": "The effect determines what happens when the policy rule is evaluated to match"}, "allowedValues": ["Audit", "<PERSON><PERSON>", "Disabled"], "defaultValue": "<PERSON><PERSON>"}}, "policyRule": {"if": {"allOf": [{"field": "type", "equals": "Microsoft.Network/networkInterfaces"}, {"field": "Microsoft.Network/networkInterfaces/ipconfigurations[*].publicIpAddress.id", "exists": "true"}, {"not": {"anyOf": [{"field": "Microsoft.Network/networkInterfaces/virtualMachine.id", "contains": "Microsoft.Compute/virtualMachineScaleSets"}, {"field": "name", "like": "*-appgw-*"}, {"field": "name", "like": "*-firewall-*"}, {"field": "name", "like": "*-vpngw-*"}, {"field": "name", "like": "*-bastion-*"}, {"field": "name", "like": "*-natgw-*"}, {"field": "name", "like": "*-lb-*"}, {"field": "tags['Service']", "in": ["ApplicationGateway", "AzureFirewall", "VPNGateway", "Bastion", "LoadBalancer", "NATGateway"]}]}}]}, "then": {"effect": "[parameters('effect')]"}}}}